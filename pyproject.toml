[project]
name = "info-collector"
version = "0.1.0"
description = "Information Collector, with multi-level fallback crawlers and Exa-MCP server"
authors = ["<PERSON> <<EMAIL>>"]

dependencies = [
  "fastapi>=0.95.0",
  "uvicorn>=0.23.0",
  "requests>=2.31.0",
  "beautifulsoup4>=4.12.2",
  "playwright>=1.30.0",
  "selenium>=4.8.0",
  "crawl4ai[all]>=0.6.0",
  "exa-py>=0.7.0",
  "fastmcp>=2.9.0",
]

[[tool.uv.index]]
name = "pypi-cn"
url = "https://mirrors.tuna.tsinghua.edu.cn/simple"
default = true

[tool.crawl4ai]
# 若需，配置并发、代理等

[tool.exa_mcp_server]
# 可定义启用的工具列表
# tools = ["web_search_exa","crawling"]
