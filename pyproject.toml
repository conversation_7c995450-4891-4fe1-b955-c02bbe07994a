[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "information-collection-agent"
version = "1.0.0"
description = "A2A-compatible information collection agent group using Crawl4AI"
readme = "README.md"
license = "MIT"
requires-python = ">=3.11"
authors = [
    { name = "Information Collection Team" },
]
keywords = [
    "a2a",
    "agent",
    "crawling",
    "information-collection",
    "crawl4ai",
    "web-scraping"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    # Core framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",

    # HTTP client
    "aiohttp>=3.9.0",
    "httpx>=0.25.0",

    # Web crawling
    "crawl4ai>=0.4.0",
    "playwright>=1.40.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",

    # Data processing
    "pandas>=2.1.0",
    "numpy>=1.24.0",

    # Utilities
    "python-dotenv>=1.0.0",
    "python-multipart>=0.0.6",
    "jinja2>=3.1.0",

    # Optional: Exa integration
    "exa-py>=1.0.0",
]

[project.optional-dependencies]
exa = [
    "exa-py>=1.0.0",
]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]

[project.scripts]
info-leader = "agents.leader.main:main"
crawl4ai-employee = "agents.employees.crawl4ai.main:main"

[[tool.uv.index]]
name = "pypi-cn"
url = "https://mirrors.tuna.tsinghua.edu.cn/simple"
default = true

[tool.crawl4ai]
# 若需，配置并发、代理等

[tool.exa_mcp_server]
# 可定义启用的工具列表
# tools = ["web_search_exa","crawling"]
