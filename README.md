# 🧠 Information Collection Group 设计文档（符合 A2A Agent Card 规范）

> 本文档基于 A2A 协议对 `information_collection_group` 系统的架构设计进行详细规范，确保具备组内调度、跨组协作、Agent Card 注册标准、服务发现与多类型 employee 协同执行能力。

---

## 🎯 核心设计目标

* 支持结构化官网信息抓取（Crawl4AI）
* 支持网页内容补全（Exa via SDK）
* 支持 Playwright / Requests / Selenium 降级抓取机制
* 支持 Employee 跨组协作（非绑定于单一 Leader）
* 符合 A2A Agent Card 协议，支持 Discovery 与注册中心兼容
* 支持 Docker 部署，独立服务暴露与日志监控

---

## 📦 技术栈 & 通信协议

| 模块类别       | 技术方案                                                         |
| ---------- | ------------------------------------------------------------ |
| Agent 容器框架 | [FastMCP](https://github.com/flexflowai/fastmcp) （支持 A2A 协议） |
| 官网抓取       | [Crawl4AI](https://docs.crawl4ai.com/)                       |
| 信息补全       | [Exa SDK](https://github.com/exa-labs/exa-py)                |
| 通信协议       | JSON-RPC2 + SSE (Stream)                                     |
| 数据传输格式     | A2A Agent Card JSON Schema                                   |
| 启动工具       | uv + Python 3.11+（支持国内源配置）                                   |

---

## 🧠 架构拓扑图

```markdown
Manager
 └─→ Information Leader Agent (FastMCP @8231)
       ├─→ Crawl4AI Employee MCP (@8232)
       └─→ Exa Employee MCP (@8233)

```

> ⚠️ 组内多个 employee 由 Leader 管理，但所有 employee 也注册在 Discovery 中，可被其他组调用。

---

## 🪪 Agent Card 示例（标准 A2A 格式）

### 🔹 1. Leader Agent Card

```json
{
  "name": "InformationLeaderAgent",
  "version": "1.0",
  "description": "Agent managing information collection employees",
  "endpoint": "http://info-leader:8231/a2a",
  "capabilities": [],
  "skills": ["agent-dispatch", "group-control"],
  "supports": { "streaming": true, "pushNotifications": true },
  "auth": ["none"],
  "protocolVersion": "1.0"
}
```

### 🔹 2. Crawl4AI Employee Card

```json
{
  "name": "Crawl4AIEmployee",
  "version": "1.0",
  "description": "Structured data crawler for official websites",
  "endpoint": "http://info-crawl4ai:8232/a2a",
  "capabilities": [
    {
      "name": "crawl_website",
      "description": "Fetch and structure official website content",
      "inputs": [{ "type": "text", "name": "url" }],
      "outputs": [{ "type": "object", "name": "structured_content" }]
    }
  ],
  "skills": ["crawl", "parse", "html"],
  "supports": { "streaming": false, "pushNotifications": false },
  "auth": ["none"],
  "protocolVersion": "1.0"
}
```

### 🔹 3. Exa Employee Card（非服务端暴露）

```json
{
  "name": "ExaSearchEmployee",
  "version": "1.0",
  "description": "MCP service wrapping Exa SDK for info enrichment",
  "endpoint": "http://exa-employee:8233/a2a",
  "capabilities": [
    {
      "name": "search_exa",
      "description": "Use Exa API to search and expand",
      "inputs": [{ "type": "text", "name": "query" }],
      "outputs": [{ "type": "object", "name": "exa_results" }]
    }
  ],
  "skills": ["search", "enrich", "LLM"],
  "supports": { "streaming": false, "pushNotifications": false },
  "auth": ["apiKey"],
  "protocolVersion": "1.0"
}

```

---

## 🗂️ 项目结构建议

```bash
information_collection_group/
├── leader/
│   ├── main.py                # FastMCP 实例
│   ├── agent_card.json        # Leader Card
│   ├── employees/
│   │   ├── crawl4ai_employee.py  # 独立服务暴露
│   │   └── exa_employee.py       # 调用 Exa API
│   └── Dockerfile
├── docker-compose.yml
├── pyproject.toml
├── README.md
└── cards/                     # 统一放置 employee agent cards
```

---

## 🧪 Use Case 1：标准官网信息爬取 + 补全

```mermaid
sequenceDiagram
    participant User
    participant Manager
    participant Leader
    participant Crawl4AI
    participant Exa

    User->>Manager: 查询“特斯拉”官网信息
    Manager->>Leader: 请求任务分派
    Leader->>Crawl4AI: 爬取官网内容
    Crawl4AI-->>Leader: 返回结构化内容
    Leader->>Exa: 调用 API 搜索信息补全
    Exa-->>Leader: 返回补全结果
    Leader-->>Manager: 汇总后返回结果
    Manager-->>User: 展示整合后的信息
```

---

## 🧪 Use Case 2：crawl4ai 失败后 fallback 降级 + exa 扩展

* Crawl4AI employee 返回超时或 500
* fallback\_chain 接管，尝试 Playwright/Selenium/Requests
* 返回 raw HTML → 传入 exa\_employee 进行上下文生成
* 最终结构同样可用

---

## ✅ 跨组 Employee 协作机制（简要流程）

1. 所有 Employee 启动后将自身 Card 注册到 Redis/Service Registry
2. Manager 下发复杂任务 → 各组 Leader 拆解
3. 每个 Leader 可从 registry 中选择本组或其他组 employee
4. 并行协作或有序执行
